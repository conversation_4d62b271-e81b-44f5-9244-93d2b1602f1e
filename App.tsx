
import React, { useEffect } from 'react';
import Navbar from './components/Navbar';
import Header from './components/Header';
import AboutUsSection from './components/AboutUsSection';
import ProductsSection from './components/ProductsSection';
import ContactSection from './components/ContactSection';
import Footer from './components/Footer';

const App: React.FC = () => {
  useEffect(() => {
    // Smooth scroll for anchor links
    const anchors = document.querySelectorAll('a[href^="#"]');
    anchors.forEach(anchor => {
      anchor.addEventListener('click', function (this: HTMLAnchorElement, e: MouseEvent) {
        const hrefAttribute = this.getAttribute('href');
        if (hrefAttribute && hrefAttribute.startsWith("#")) {
          const targetId = hrefAttribute.substring(1);
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            e.preventDefault();
            targetElement.scrollIntoView({
              behavior: 'smooth'
            });
          }
        }
      });
    });
  }, []);

  return (
    <div className="font-inter bg-[#FDFCFB]">
      <Navbar />
      <Header />
      <AboutUsSection />
      <ProductsSection />
      <ContactSection />
      <Footer />
    </div>
  );
};

export default App;
