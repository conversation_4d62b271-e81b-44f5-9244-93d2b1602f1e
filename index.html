
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cukiernia Gruszecki - Tradycja i Elegancja od 1968 - Poznań, Ogrody</title>
    <meta name="description" content="Cukiernia Gruszecki w Poznaniu przy ul. Dąbrowskiego 152 (Ogrody). Eleganckie wnętrza, tradycy<PERSON>e wypieki, wyśmienite lody, ciasta na zamówienie. Zapraszamy!">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Inter:wght@300;400;500;700&family=Dancing+Script:wght@600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-dark-brown': '#4A3B31',
                        'brand-medium-brown': '#7B6D63',
                        'brand-light-beige': '#F5EFE6',
                        'brand-gold-accent': '#C0A080',
                        'brand-muted-pink': '#D8BFD8',
                        'footer-bg': '#312A24',
                    },
                    fontFamily: {
                        playfair: ['"Playfair Display"', 'serif'],
                        inter: ['Inter', 'sans-serif'],
                        dancing: ['"Dancing Script"', 'cursive'],
                    },
                },
            },
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif; /* Default font from Tailwind config */
            scroll-behavior: smooth; /* Handled by JS for wider browser support and specific targeting */
            background-color: #FDFCFB; /* Very light, almost white page background */
        }
        /* Styles for pseudo-elements that are hard to do with pure Tailwind CDN */
        .history-section-content::before, 
        .history-section-content::after {
            content: '';
            position: absolute;
            width: 80px; /* Slightly wider for better visual balance */
            height: 2px;
            background-color: #C0A080; /* brand-gold-accent */
            left: 50%;
            transform: translateX(-50%);
            border-radius: 1px; /* Soften the edges */
        }
        .history-section-content::before { 
            top: 1rem; /* Adjusted spacing */
        }
        .history-section-content::after { 
            bottom: 1rem; /* Adjusted spacing */
        }

        /* Smooth scroll for anchor links targeted by JS */
        html {
            scroll-behavior: smooth;
        }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.0.1"
  }
}
</script>
</head>
<body class="text-gray-800 antialiased">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
