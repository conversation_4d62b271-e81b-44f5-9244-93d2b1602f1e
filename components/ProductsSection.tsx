import React from 'react';
import SectionTitle from './SectionTitle';
import ProductCard from './ProductCard';

// Define a type for the product for clarity
interface ProductDefinition {
  id: string;
  title: string;
  description: string;
  altText: string;
  imageUrl: string;
}

const initialProducts: ProductDefinition[] = [
  {
    id: "cakes",
    title: "Ciasta i Torty",
    description: "Poczuj smak tradycji w każdym kęsie naszych tortów i ciast. Od aksamitnych serników, przez puszyste biszkopty idealne do kawy, po spektakularne torty na niezapomniane okazje – wszystko tworzone z pasją i najlepszych składników.",
    altText: "Elegancki tort czekoladowy z bogatą dekoracją",
    imageUrl: "/ciasta.jpeg"
  },
  {
    id: "cookies",
    title: "Ciasteczka i Drożdżówki",
    description: "Chrupiące na zewnątrz, miękkie w środku – nasze ciasteczka to małe dzieła sztuki. Aromat świeżo wypieczonych drożdżówek i maślanych rogalików przywita Cię już od progu. Idealne na słodki start dnia lub chwilę przyjemności.",
    altText: "Apetyczny wybór rzemieślniczych ciasteczek na talerzu",
    imageUrl: "/ciastka.jpeg"
  },
  {
    id: "icecream",
    title: "Lody Rzemieślnicze",
    description: "Zanurz się w kremowej rozkoszy naszych lodów rzemieślniczych. Tworzone na miejscu, wyłącznie z naturalnych składników, codziennie zaskakują intensywnością smaku – od ponadczasowej klasyki po ekscytujące sezonowe kompozycje.",
    altText: "Kolorowe, kremowe gałki lodów rzemieślniczych w pucharku",
    imageUrl: "/lody.jpeg"
  }
];

const ProductsSection: React.FC = () => {
  return (
    <section id="produkty" className="py-20 md:py-32 bg-gradient-to-b from-brand-light-beige/60 to-brand-light-beige">
      <div className="container mx-auto px-6">
        <SectionTitle text="Nasze Specjały" />
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-10 md:gap-12 lg:gap-14">
          {initialProducts.map((product) => (
            <ProductCard
              key={product.id}
              imageUrl={product.imageUrl}
              title={product.title}
              description={product.description}
              altText={product.altText}
              isLoading={false}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;