import React from 'react';

const Header: React.FC = () => {
  const heroStyle = {
    // Changed to a new image ID, aiming for a more upscale patisserie feel
    backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.7)), url('/tlo.jpeg')`, 
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  };

  return (
    <header style={heroStyle} className="text-white relative" aria-label="Witryna cukierni Gruszecki - Słodka Tradycja">
      <div className="container mx-auto px-6 py-32 md:py-48 lg:py-64 text-center relative z-10">
        <h1 
          className="font-playfair text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-6"
          style={{ textShadow: '2px 2px 8px rgba(0,0,0,0.7)' }}
        >
          Słodka Tradycja od 1968 Roku
        </h1>
        <p 
          className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto font-light"
          style={{ textShadow: '1px 1px 4px rgba(0,0,0,0.6)' }}
        >
          Odkryj smak prawdziwych, domowych wypieków i lodów w sercu poznańskich Ogrodów.
        </p>
        <a 
          href="#produkty" 
          className="inline-block bg-brand-gold-accent text-white font-semibold py-3 px-8 rounded-md shadow-lg hover:bg-opacity-90 transition-all duration-300 transform hover:scale-105 tracking-wider text-lg hover:shadow-xl"
        >
          Zobacz nasze słodkości
        </a>
      </div>
    </header>
  );
};

export default Header;