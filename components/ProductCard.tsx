
import React from 'react';

interface ProductCardProps {
  imageUrl: string | null;
  title: string;
  description: string;
  altText: string;
  isLoading: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ imageUrl, title, description, altText, isLoading }) => {
  return (
    <div className="bg-gradient-to-b from-white to-brand-light-beige/20 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 flex flex-col border border-gray-200/70 transform hover:-translate-y-2 hover:scale-105 overflow-hidden backdrop-blur-sm">
      <div className="w-full h-64 md:h-56 bg-gray-200 flex items-center justify-center relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200/50">
            <div className="text-brand-medium-brown animate-pulse text-sm">Ładowanie...</div>
          </div>
        )}
        {imageUrl && !isLoading ? (
          <img
            src={imageUrl}
            alt={altText || title}
            className="w-full h-full object-cover"
            loading="lazy"
            onError={(e) => {
              // Optional: Handle image load errors for picsum, e.g., show a fallback
              // For simplicity, we'll let the browser show its default broken image icon
              console.warn(`Failed to load image: ${imageUrl}`);
              const target = e.target as HTMLImageElement;
              target.style.display = 'none'; // Hide broken image icon
              // You could also replace it with a placeholder div
              const parent = target.parentElement;
              if (parent) {
                const errorDiv = parent.querySelector('.image-error-placeholder');
                if (errorDiv) (errorDiv as HTMLElement).style.display = 'flex';
              }
            }}
          />
        ) : !isLoading && !imageUrl ? (
           <div className="text-brand-medium-brown p-4 text-center text-sm">Brak obrazka</div>
        ) : null}
         {/* Fallback visible if image fails to load and onError hides the img tag */}
        <div className="image-error-placeholder absolute inset-0 items-center justify-center text-brand-medium-brown p-4 text-center text-sm" style={{display: 'none'}}>
            Nie udało się załadować obrazka.
        </div>
      </div>
      <div className="p-8 text-center flex flex-col flex-grow">
        <h3 className="text-2xl md:text-3xl font-inter font-bold text-brand-dark-brown mb-4 tracking-wide">{title}</h3>
        <div className="w-16 h-0.5 bg-brand-gold-accent mx-auto mb-4 rounded-full"></div>
        <p className="text-brand-medium-brown text-base md:text-lg leading-relaxed flex-grow font-inter font-light">{description}</p>
      </div>
    </div>
  );
};

export default ProductCard;