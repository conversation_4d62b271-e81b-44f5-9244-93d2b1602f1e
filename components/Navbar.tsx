
import React from 'react';

const NavLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => (
  <a
    href={href}
    className="text-brand-medium-brown hover:text-brand-gold-accent transition-colors duration-300 font-medium pb-1 border-b-2 border-transparent hover:border-brand-gold-accent text-sm sm:text-base"
  >
    {children}
  </a>
);

const Navbar: React.FC = () => {
  return (
    <nav className="bg-white/95 backdrop-blur-md shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
        <a href="#" className="font-playfair text-2xl sm:text-3xl font-bold text-brand-dark-brown">
          <PERSON>uki<PERSON><PERSON> Grus<PERSON>
        </a>
        <div className="space-x-4 sm:space-x-6">
          <NavLink href="#o-nas">O nas</NavLink>
          <NavLink href="#produkty">Produkty</NavLink>
          <NavLink href="#kontakt">Kontakt</NavLink>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
