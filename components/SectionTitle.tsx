
import React from 'react';

interface SectionTitleProps {
  text: string; // Example: "Poznaj Historię Nasz<PERSON>"
}

const SectionTitle: React.FC<SectionTitleProps> = ({ text }) => {
  if (!text || text.length === 0) {
    return null;
  }

  const firstLetter = text.charAt(0);
  const restOfTitle = text.substring(1);

  return (
    <h2 className="font-dancing text-5xl md:text-6xl text-center mb-12 md:mb-16 text-brand-dark-brown">
      <span className="text-7xl md:text-8xl text-brand-gold-accent mr-1">{firstLetter}</span>
      {restOfTitle}
    </h2>
  );
};

export default SectionTitle;
