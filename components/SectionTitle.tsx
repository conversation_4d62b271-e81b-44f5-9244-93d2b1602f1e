
import React from 'react';

interface SectionTitleProps {
  text: string; // Example: "Poznaj Historię <PERSON>"
}

const SectionTitle: React.FC<SectionTitleProps> = ({ text }) => {
  if (!text || text.length === 0) {
    return null;
  }

  const firstLetter = text.charAt(0);
  const restOfTitle = text.substring(1);

  return (
    <div className="text-center mb-16 md:mb-20">
      <h2 className="font-dancing text-6xl md:text-7xl lg:text-8xl text-brand-dark-brown relative inline-block">
        <span className="text-8xl md:text-9xl lg:text-9xl text-brand-gold-accent mr-2 drop-shadow-lg">{firstLetter}</span>
        <span className="drop-shadow-md">{restOfTitle}</span>
      </h2>
      <div className="mt-6 flex justify-center">
        <div className="w-24 h-1 bg-brand-gold-accent rounded-full"></div>
      </div>
    </div>
  );
};

export default SectionTitle;
